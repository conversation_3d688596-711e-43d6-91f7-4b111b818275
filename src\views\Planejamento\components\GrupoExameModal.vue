<template>
  <div class="modal fade" id="grupoExameModal" tabindex="-1" aria-labelledby="grupoExameModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-gradient-primary">
          <h5 class="modal-title text-white" id="grupoExameModalLabel">
            <i class="fas fa-layer-group me-2"></i>
            Criar Grupo de Modelos 3D
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Informações do Grupo -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-gradient text-primary mb-3">
                <i class="fas fa-info-circle me-2"></i>
                Informações do Grupo
              </h6>
              <div class="row">
                <div class="col-md-8">
                  <div class="input-group input-group-outline mb-3">
                    <label class="form-label">Nome do Grupo</label>
                    <input 
                      type="text" 
                      class="form-control"
                      v-model="grupoData.nome"
                      placeholder="Ex: Exame Inicial - Mandíbula e Maxila"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="input-group input-group-outline mb-3">
                    <label class="form-label">Data</label>
                    <input 
                      type="date" 
                      class="form-control"
                      v-model="grupoData.data"
                    />
                  </div>
                </div>
              </div>
              <div class="input-group input-group-outline mb-3">
                <label class="form-label">Descrição (opcional)</label>
                <textarea 
                  class="form-control"
                  v-model="grupoData.descricao"
                  rows="2"
                  placeholder="Descrição adicional sobre este grupo de modelos..."
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Upload de Modelos -->
          <div class="row">
            <div class="col-12">
              <h6 class="text-gradient text-primary mb-3">
                <i class="fas fa-upload me-2"></i>
                Modelos 3D
              </h6>
              
              <!-- Área de Upload -->
              <div 
                class="upload-area"
                :class="{ 'drag-over': isDragOver }"
                @dragover.prevent="isDragOver = true"
                @dragleave.prevent="isDragOver = false"
                @drop.prevent="onDrop"
                @click="triggerFileInput"
              >
                <input
                  ref="fileInput"
                  type="file"
                  accept=".stl"
                  multiple
                  @change="onFileSelect"
                  style="display: none;"
                />
                
                <div class="upload-content">
                  <i class="fas fa-cloud-upload-alt upload-icon"></i>
                  <p class="upload-text">
                    Arraste os arquivos STL aqui ou <span class="text-primary">clique para selecionar</span>
                  </p>
                  <small class="text-muted">Suporte para múltiplos arquivos STL</small>
                </div>
              </div>

              <!-- Lista de Arquivos Selecionados -->
              <div v-if="selectedFiles.length > 0" class="selected-files mt-3">
                <h6 class="mb-3">Arquivos Selecionados:</h6>
                <div class="row">
                  <div 
                    v-for="(file, index) in selectedFiles" 
                    :key="index"
                    class="col-md-6 mb-3"
                  >
                    <div class="file-card">
                      <div class="file-info">
                        <i class="fas fa-cube text-primary me-2"></i>
                        <span class="file-name">{{ file.name }}</span>
                        <button 
                          type="button"
                          class="btn btn-sm btn-outline-danger ms-auto"
                          @click="removeFile(index)"
                        >
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                      <div class="input-group input-group-outline mt-2">
                        <label class="form-label">Descrição</label>
                        <input 
                          type="text" 
                          class="form-control form-control-sm"
                          v-model="fileDescriptions[index]"
                          placeholder="Ex: Mandíbula, Maxila..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn bg-gradient-primary"
            @click="criarGrupo"
            :disabled="!canCreate || isCreating"
          >
            <i v-if="isCreating" class="fas fa-spinner fa-spin me-2"></i>
            <i v-else class="fas fa-save me-2"></i>
            {{ isCreating ? 'Criando...' : 'Criar Grupo' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.upload-area {
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #0d6efd;
  background: rgba(13, 110, 253, 0.05);
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 3rem;
  color: #0d6efd;
  margin-bottom: 1rem;
}

.upload-text {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #666;
}

.selected-files {
  max-height: 300px;
  overflow-y: auto;
}

.file-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.file-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  flex: 1;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.text-gradient {
  background: linear-gradient(45deg, #0d6efd, #6c757d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}
</style>

<script>
import { criarGrupoExame, uploadModelosParaGrupo } from "@/services/modelos3dService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "GrupoExameModal",
  props: {
    paciente: {
      type: Object,
      required: true
    }
  },
  emits: ["grupoCreated"],
  data() {
    return {
      grupoData: {
        nome: '',
        data: new Date().toISOString().slice(0, 10),
        descricao: ''
      },
      selectedFiles: [],
      fileDescriptions: [],
      isDragOver: false,
      isCreating: false
    };
  },
  computed: {
    canCreate() {
      return this.grupoData.nome.trim() && 
             this.grupoData.data && 
             this.selectedFiles.length > 0;
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    
    onFileSelect(event) {
      this.handleFiles(Array.from(event.target.files));
    },
    
    onDrop(event) {
      this.isDragOver = false;
      const files = Array.from(event.dataTransfer.files).filter(file =>
        file.name.toLowerCase().endsWith('.stl')
      );
      this.handleFiles(files);
    },
    
    handleFiles(files) {
      if (files.length === 0) {
        cSwal.cError("Por favor, selecione arquivos STL válidos.");
        return;
      }
      
      this.selectedFiles = [...this.selectedFiles, ...files];
      this.fileDescriptions = [
        ...this.fileDescriptions,
        ...Array(files.length).fill('')
      ];
    },
    
    removeFile(index) {
      this.selectedFiles.splice(index, 1);
      this.fileDescriptions.splice(index, 1);
    },
    
    async criarGrupo() {
      if (!this.canCreate) return;
      
      this.isCreating = true;
      
      try {
        // Criar o grupo
        const grupoResponse = await criarGrupoExame({
          paciente_id: this.paciente.id,
          nome: this.grupoData.nome,
          data: this.grupoData.data,
          descricao: this.grupoData.descricao
        });
        
        if (!grupoResponse) {
          throw new Error('Erro ao criar grupo de exame');
        }
        
        // Upload dos modelos
        const uploadResponse = await uploadModelosParaGrupo(
          grupoResponse.data.id,
          this.selectedFiles,
          this.fileDescriptions
        );
        
        if (!uploadResponse) {
          throw new Error('Erro ao fazer upload dos modelos');
        }
        
        cSwal.cSuccess("Grupo de modelos 3D criado com sucesso!");
        this.$emit("grupoCreated");
        this.resetForm();
        
        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('grupoExameModal'));
        modal.hide();
        
      } catch (error) {
        console.error('Erro ao criar grupo:', error);
        cSwal.cError("Erro ao criar grupo de modelos 3D. Tente novamente.");
      } finally {
        this.isCreating = false;
      }
    },
    
    resetForm() {
      this.grupoData = {
        nome: '',
        data: new Date().toISOString().slice(0, 10),
        descricao: ''
      };
      this.selectedFiles = [];
      this.fileDescriptions = [];
      this.isDragOver = false;
    }
  }
};
</script>
